#-*- mode: org -*-
Current new features TODO: 
* Manaweave Cloths are used in combat to disable armor
  - Activate by hitting someone with a full attack charge bar
  - Debu<PERSON> applies for 10 seconds, stack up to 30?
  - Impl requires some careful mixin/edgecasing
    - On apply, set a counter and rename Enchantments tag
    - on tick past counter, rename tag back
* Some way to auto-insert to Black Hole Talismans
  - Idea: dropped cobble BHT merges with dropped cobble? Causes problems with Crafty Crate outputs, since they output next to one another
  - Idea: Crafting recipe, cobble BHT + cobble => fuller cobble BHT? (Ran into recipe technical limitations before; might be fixed now)
* Kekimurus buff (long overdue)
  - Streaks?
  - <PERSON><PERSON><PERSON><PERSON> mentioned "potion cakes" i.e. cakes as a base in the Brewery. Maybe that???
* New relics (or maybe craftables?)
  - Reifactory Halo: Ass. Halo which accesses local Corporea net
    - [C] keybind works when held or table opened
    - Maybe chat also works when held?
    - Registered items/recipes will pull pre-crafted from net
      - Register items slightly different? Right-click with item in offhand to set?
    - Pull ingredients from net automatically?
  - Branch of Yggdrasil: Voxel sniper
    - Shift-use to bind to block; then use to place from afar
* Do something new with Gaia spirits
  - Used in advanced Plate recipes perhaps?
* Amethyst flower
  - AMETHYSTLE
  - new Amethyst Core block (or reuse another?)
  - Place core inside geode, flower outside geode
  - flower must be axis aligned w/ exactly 1 core
  - every second, flower destroys all amethyst blocks around it with no drops
  - If the destroyed blocks match the buds in relative position exactly, gen
* April Fool's: Return of the daybloom
  - Gen mana in day, pull mana *out* of nearby pools at night
  - Decay if no mana nearby?
  - Net negative if left unattended. Lmao
* Making elementium better
- Aka making pixies more enticing
- Redo the visuals, make them fly faster or attack with modes other than "ram into the
  enemy slowly"
- Upgrades on the sword/armor somehow?
