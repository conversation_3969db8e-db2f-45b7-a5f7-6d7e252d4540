name: Build and Push website

on:
  push:
    paths:
      - 'Xplat/src/main/resources/assets/botania/lang/en_us.json'
      - 'web/**'

jobs:
  determinator:
    runs-on: ubuntu-latest
    outputs:
      default_branch: ${{ steps.determinate.outputs.default_branch }}
    steps:
    - id: determinate
      env:
        GH_TOKEN: ${{ github.token }}
      run: |
        DEFAULT_BRANCH=$(gh api \
          -H "Accept: application/vnd.github+json" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          /repos/VazkiiMods/Botania | jq -r .default_branch)
        # Add refs/heads/ for easier comparison with github.ref below
        echo "default_branch=refs/heads/$DEFAULT_BRANCH" >> "$GITHUB_OUTPUT"
  main:
    runs-on: ubuntu-latest
    needs: determinator
    steps:
    - uses: actions/checkout@v4
    - name: Cache
      uses: actions/cache@v4
      with:
        path: |
          web/vendor
        key: ${{ hashFiles('web/Gemfile.lock') }}
    - name: Install deps
      working-directory: web
      run: |
        sudo apt-get install bundler
        bundle config set --local deployment 'true'
        bundle install
    - name: Build website
      working-directory: web
      run: bundle exec jekyll build
    - name: Print branch names for debugging
      run: |
        echo 'Current branch: ${{ github.ref }}'
        echo 'Default branch: ${{ needs.determinator.outputs.default_branch }}'
    - name: Import ssh keys
      if: ${{ github.ref == needs.determinator.outputs.default_branch }}
      run: |
        umask go=
        mkdir -p ~/.ssh
        echo '${{ secrets.VIOLET_MOON_WEBSERVER_SSH_KEY }}' > ~/.ssh/id_ed25519
        echo '${{ vars.BOTANIAWEB_KNOWN_HOST }}' >> ~/.ssh/known_hosts
    - name: Push
      if: ${{ github.ref == needs.determinator.outputs.default_branch }}
      run: rsync -rl web/_site/ <EMAIL>:/var/www/botaniamod_net
