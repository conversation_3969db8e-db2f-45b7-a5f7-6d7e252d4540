{"version": "0.2.0", "configurations": [{"type": "java", "request": "launch", "name": "Forge - Client", "presentation": {"group": "Mod Development - Forge", "order": 0}, "projectName": "Forge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\clientRunProgramArgs.txt"], "vmArgs": ["@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\clientRunVmArgs.txt", "-Dfml.modFolders=botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main"], "cwd": "${workspaceFolder}\\run", "env": {"MOD_CLASSES": "botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main"}, "console": "internalConsole", "shortenCommandLine": "none"}, {"type": "java", "request": "launch", "name": "Forge - Data", "presentation": {"group": "Mod Development - Forge", "order": 1}, "projectName": "Forge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\dataRunProgramArgs.txt"], "vmArgs": ["@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\dataRunVmArgs.txt", "-Dfml.modFolders=botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main"], "cwd": "${workspaceFolder}\\run", "env": {"MOD_CLASSES": "botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main"}, "console": "internalConsole", "shortenCommandLine": "none"}, {"type": "java", "request": "launch", "name": "Forge - Server", "presentation": {"group": "Mod Development - Forge", "order": 2}, "projectName": "Forge", "mainClass": "net.neoforged.devlaunch.Main", "args": ["@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\serverRunProgramArgs.txt"], "vmArgs": ["@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\serverRunVmArgs.txt", "-Dfml.modFolders=botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main"], "cwd": "${workspaceFolder}\\run", "env": {"MOD_CLASSES": "botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main"}, "console": "internalConsole", "shortenCommandLine": "none"}]}