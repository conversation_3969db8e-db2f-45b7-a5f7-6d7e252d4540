<?xml version="1.0" ?>
<launchConfiguration type="org.eclipse.jdt.launching.localJavaApplication">
    <stringAttribute key="org.eclipse.jdt.launching.PROJECT_ATTR" value="Forge"></stringAttribute>
    <stringAttribute key="org.eclipse.jdt.launching.MAIN_TYPE" value="net.neoforged.devlaunch.Main"></stringAttribute>
    <stringAttribute key="org.eclipse.jdt.launching.PROGRAM_ARGUMENTS" value=" &quot;@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\dataRunProgramArgs.txt&quot;"></stringAttribute>
    <stringAttribute key="org.eclipse.jdt.launching.VM_ARGUMENTS" value=" &quot;@G:\\Idea Project\\Botania\\Forge\\build\\moddev\\dataRunVmArgs.txt&quot; &quot;-Dfml.modFolders=botania%%G:\\Idea Project\\Botania\\Forge\\bin\\main;botania%%G:\\Idea Project\\Botania\\Xplat\\bin\\main&quot;"></stringAttribute>
    <mapAttribute key="org.eclipse.debug.core.environmentVariables">
        <mapEntry key="MOD_CLASSES" value="botania%%G:\Idea Project\Botania\Forge\bin\main;botania%%G:\Idea Project\Botania\Xplat\bin\main"></mapEntry>
    </mapAttribute>
    <stringAttribute key="org.eclipse.jdt.launching.WORKING_DIRECTORY" value="G:\Idea Project\Botania\run"></stringAttribute>
    <booleanAttribute key="org.eclipse.jdt.launching.STOP_IN_MAIN" value="false"></booleanAttribute>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_ATTR_USE_ARGFILE" value="false"></booleanAttribute>
</launchConfiguration>