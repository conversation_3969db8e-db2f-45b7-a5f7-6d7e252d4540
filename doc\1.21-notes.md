### Mandatory porting tasks
* Java 17 -> Java 21
* switch item data from NBT to item components
* codecs everywhere
* Forge -> NeoForge checklist:
  * toolchain, packages: https://neoforged.net/news/20.2release/
  * events system: https://neoforged.net/news/20.2eventbus-changes/
  * registries: https://neoforged.net/news/20.2registry-rework/
  * mixins: https://neoforged.net/news/20.3release/
  * capabilities: https://neoforged.net/news/20.3capability-rework/
  * networking: https://neoforged.net/news/20.4networking-rework/
  * major 1.20.5 changes (item components, Java 21, and more): https://neoforged.net/news/20.5release/
  * 1.21 (damage-related events, a few deprecations): https://neoforged.net/news/21.0release/
* Fabric/vanilla update checklist:
  * 1.20.2 (mostly networking): https://fabricmc.net/2023/09/12/1202.html
  * 1.20.3/.4 (/tick support, block API changes, etc.): https://fabricmc.net/2023/11/30/1203.html
  * 1.20.5/.6 (Java 21, item components, networking again): https://fabricmc.net/2024/04/19/1205.html
  * 1.21 (enchantments, data pack paths, rendering): https://fabricmc.net/2024/05/31/121.html

### Technical/internal changes
* We are going to want to try to support world upgrades from 1.20, will need datafixers
* ~~Remove all random bits of legacy data support code; if we want to support world upgrades from pre-1.20.5 (which seems unlikely), we are going to need data fixer logic, which could also take care of legacy block entity properties, etc.~~
* Add MixinExtras dependency to allow for less hacky feature/fix implementations, e.g.:
  * zoom for Botania bows
  * apothecary filling via dripstone
* Use Fabric Mixin instead of the SpongePowered version in Xplat module to take full advantage of both Fabric and NeoForge shipping with that fork
* Use convention tags v2, since Fabric and NeoForge unified their tag systems
  * ensure existing resources are tagged properly (e.g. manasteel ingots as `#c:ingots/manasteel`)
  * remove platform-specific tags (except for mod compatibility where we know they stay on only one of the loaders)
  * update recipes to preferably use item tags, where it makes sense
  * update Vitreous Pickaxe to check for `#c:glass_blocks` and `#c:glass_panes`, ideally wrapped in a separate block tag
    * maybe also set it as appropriate tool for glass blocks (in addition to the usual pickaxe blocks)
    * maybe add other glass-like blocks without a vanilla tool to it (glowstone, sea lanterns, redstone lamp, beacon)
* Transactional mana transfers and corporea requests
  * Corporea requests should be operating within Fabric Transfer API transactions instead of defining top-level transactions per request
  * Mana requests (and probably also block providers) should be transactional within a player's inventory to avoid awkward interactions in low-mana situations
  * Can we maybe implement some kind of "attempt to put item back if transaction is rolled back" feature into NeoForge Corporea?
* Move certain block entity attributes into block state properties
  * floating flower island type (including a new type for enchanted soil)
  * mana spreader scaffolding cover
  * animated torch (target) direction (maybe, would make the initialization less weird, and allow observers to detect change in direction)
* Flattern certain block entity attributes or block properties into block types
  * mana spreader cover colors (to be able to tag them as `#c:dyed` and `#minecraft:dampens_vibrations`)
  * mana pool dye colors (maybe even turn "fabulous" into a separate "color" somehow?)
* Get rid of some enum-based behavioral changes:\
  (A lot of the enum preset variant approaches can probably convert the enum type to a record type, if they aren't outright replaced by different approaches.)
  * Luminizer types should be subclasses
  * Grass types should probably just be subclasses of `SpreadingSnowyDirtBlock`
  * Spark augment types should be identified by item type
  * Crafty Crate patterns are already translated from and to booleans per slot, maybe the slot configuration should not be restricted, allowing data packs to provide additional flexibility here
  * `OptionalDyeColor` can go away if mana pool colors are flattened into block types
  * Mana pool variants (diluted, default size, or creative) should be subclasses, not enum-switched properties (fabulous should probably be a color variant)
  * Mana spreader variants should not be stored as enum reference, but as presets (including flags for redstone trigger behavior and rotating color rendering)
  * Stew effects for mystical flowers should not be defined in a private `DyeColor` switch referenced within the `BotaniaFlower` constructor
  * Platform block types should be separate subclasses instead of using a variant enum to hard-code the existing types
  * Natura pylon should be a separatre subclass, and pylon type variants should not be enum references
  * Figure out if there's an alternative to the variant enum and massive switch cases in `CosmeticBaubleItem`
  * Horn/drum variants should be subclasses instead of having a type/variant enum that is being switched over
* Elementium armor pieces (other than helmet) could be merged into `ElementiumArmorItem` with an additional constructor parameter for the pixie spawn chance (instead of duplicating the attribute modifier logic)
  * That is, unless they get more varied effects than just different spawn chances (see [this forum thread](https://forum.violetmoon.org/d/270-elementium-toolsarmor-are-they-good-or-do-they-need-a-buff))
* Use customized explosion behavior for key explosions instead of EntityMixin
* Move a whole lot of item data over to components (likely including CCA item components and potentially even some Forge item capabilities)
* Make more registries dynamic/datapackable, e.g. brews
* Make verified multiblocks (alfheim portal frame, terra plate base, enchanter setup, gaia arena without beacon base) datapackable, but with plausibility checks
* Set up `package-info.java` with `@FieldsAreNonnullByDefault`, `@MethodsReturnNonnullByDefault`, and `@ParametersAreNonnullByDefault` (as Minecraft, Fabric API, and NeoForge do) to cut down the number of required `@NonNull` annotations everywhere
* Change `Bound.UNBOUND_POS` and `ManaBurst.NO_SOURCE` to be proper `Optional`s instead of having a `(0, MIN_INT, 0)` position.

### Feature candidates (potentially breaking changes)
* nice-to-have Patchouli features we could use, that are yet to be merged:
  * ~~generic advancement trigger for reading a book (PR #715)~~ -> done, switch lexicon use trigger over to Patchouli read trigger
  * proper localization for book editions (PR #648)
* Runic altar recipes:
  * reagent definitions in recipe instead of hard-coded Livingrock
    (recipe viewer support already exists, it just needs to be hooked up once the additional input is available)
  * support generic catalyst items for altar recipes, i.e. items which are required but will be returned upon completion of the recipe
    * change rune inputs to be catalysts
  * Figure out a way to not just make a head for a player's current skin, but also allow historic skins somehow
* Clayconia upgrade — remove it if it doesn't work out?
  * somehow support mud->clay, maybe even recipe support (but avoid becoming another Orechid variant)
  * maybe become an Agricarnation for minerals? (boost mud conversion speed, pointed dripstone effects, amethyst grows, etc.)
* pre/post mcfunction support for block-swapping recipes (Pure Daisy, Marimorphosis, Orechids)
  * maybe even for item outputs? (petal apothecary, runic altar, mana infusion, terra plate, potentially clayconia)
* Paintslinger Lens support for connected blocks, block entities, and generic entities
  * replace hard-coded sheep/spark handling with registered painting functions for entity types
    * needs to pick single entity or AoE radius (the latter should scale by burst size)
    * default support: sheep, sparks, shulkers
    * maybe default compat for Quark's dyed item frames?
  * add support for connected blocks (e.g. beds, some kind of painted chests, modded maybe multi-blocks)
  * add support for block entities (e.g. shulker boxes, beds, moving paintable blocks)
    * won't support covered mana spreaders or dyed mana pools, since they capture the burst – unless…
* switch more block/item processing flowers to use individual tags (or even recipes) instead of hard-coded block/item types or generic tags
  * Hydroangeas (fluid tag `#botania:hydroangeas_consumable`, including just water by default)
    (also upgrade infinite source detection to query the consumed fluid's actual spreading logic)
  * Thermalily (fluid tag `#botania:thermalily_consumable`, includes only lava by default)
    (maybe also allow grabbing fluids from cauldrons)
  * Munchdew (block tag `#botania:munchdew_consumable`, includes only vanilla leaves tag by default)
  * Clayconia (block tags `#botania:clayconia_converts_to_clayball` and `#botania:clayconia_converts_to_clay_block`, unless we decide to go the recipe route)
  * Bubbell (switch to fluid tag `#botania:bubbell_displaceable`, maybe even support drying waterlogged blocks)
    * fake air blocks will need to remember which fluid they actually displaced
* armor trim support
  * redesign of the Botania armor models to match UV mapping of vanilla armors, such that non-vanilla parts are moved to unused areas of the texture
  * add Botania trim materials (probably manasteel, elementium, terrasteel, mana diamond, dragonstone, and the quartz variants)
* Orechid recipe adjustments
  * remove ancient debris from Orechid Ignem (replace with Loonium in Bastion Remnant)
  * maybe extend biome bonus weight support from Marimorphosis to all Orechid-like recipes
    * adjust Orechid stone recipes accordingly (more emeralds in mountains, more copper in dripstone caves, etc.)
    * maybe even remove Orechid Ignem as a result and let the regular Orechid handle these recipes with a biome restriction, especially in combination with data-driven configuration
  * maybe add data-driven Orechid configurations to replace hard-coded GoG changes (mana cost per input item, operation speed)
    * GoG could eventually become an independent data pack instead of having all kinds of exceptions hard-coded into Botania
* Armadillo: add it to Cocoon of Caprice as rare spawn, maybe find something to do with its scutes
* Nether variants/behavior for pasture seed variants (either reuse existing seed types or make new ones to make nylium) and floral fertilizer (spawn mushrooms on nylium)
* Switch ender air collection to use managlass vials and maybe also alfglass flasks instead of glass bottles
  * Ender Air Vials will be the same as current ender air bottles for throwing and crafting, except obviously return the vial instead of an empty bottle.
  * The old Ender Air Bottles would need to remain as legacy item that is no longer survival-obtainable, but still converts back into empty vanilla glass bottles when used for crafting. (Would they? I think turning them to vials would be fine)
  * If Ender Air Flasks are a thing there are two options to deal with them. Either they are the same as the vials, or they have double the capacity. The latter could have multiple consequences:
    * They might create a slightly larger ender air cloud when thrown, or have a higher chance of causing ghasts to drop a tear. Larger clouds could be collected twice by empty vials.
    * They might be good for two crafting operations instead of one, leaving behind some kind of half-used ender air flask, which behaves similarly to ender air vials.
    * Pro: Larger ender air capacity would be consistent with the flask's larger brew capacity.
    * Con: Probably an automation headache, since full and half-used flasks do not stack with each other and count as completely different items for filtering. (imo not really a problem because one could use a vial to avoid this entirely)
* Adjust order of colored items in UIs to match vanilla sorting (which is different from the order in `DyeColor`)
  * Item order in creative inventory for flowers, petals, etc.
  * Storage order in flower pouch (needs data fixer to migrate to item components — item components will be a breaking change, world update support may or may not happen)
* Crafty Crate is dead, long live the Crafter! — Do we repurpose crafting placeholders and patterns for use with the Crafter or do we remove them as well?
  * Crafting patterns could be used to instantly reconfigure a crafter's slots
  * Crafting placeholders could act as empty slots in any shaped crafting recipe, unless there's specifically a matching recipe that includes them
* Spellbinding Cloth has been redundant for a while (with both the grind stone and the Rosa Arcana), except for the removal of curses — remove or improve it?
  * Idea: Have it pull off exactly one enchantment from the item into the cloth, where the enchantment is stored similarly to an enchanted book. TODO: Should this use crafting or some other mechanic? And how are the enchantments applied to other items again?
* Eye of the Ancients: Add toggle between adult and baby animals to make it more useful
* Petal Pouch — variant of flower pouch that stores petals and glimmering mushrooms
  * automatically breaks down flowers into their petal form on pickup
  * maybe provide some kind of helper mechanism for selecting petals for the apothecary?
* Try to finally get those mana API changes in
  * Finer control of burst targeting and firing. For example allowing phantom bursts to pass through full spreaders/pools
  * Mana splitter that shoots bursts (though nerf to not renew burst lifespan lol)
* (This would be a big change with lots of consequences; Requires thorough discussion and testing) Consider tweaking the requirements for spreaders firing. For example, completely ignore fired bursts and fire at a fixed interval. Or maybe allow three bursts in flight at the same time.
* Expected order of colors for Spectrolus depends on world seed
* Allow Life Aggregator to move Trial Spawners (maybe even Trial Vaults?)

### Development period
I think for 1.21 i want to have a somewhat long beta period while we get everything in order and do all the breaking changes we want to do (as you can see there are a lot planned). Also to get community feedback on the more invasive changes.

It is also not clear yet, if the next supported version will actually be 1.21.1.